#!/usr/bin/env python3
"""
Unified Model Testing Framework

Combines simple model tests and progressive complexity tests into a single comprehensive suite.
Creates unified logs with enhanced token information.
"""
import os
import sys
import time
from dataclasses import dataclass
from typing import Dict, List, Any, Optional

from dotenv import load_dotenv

if os.path.exists(".env"):
    load_dotenv()

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import get_model_config, LLAMACPP_API_URL
from models.llamacpp_provider import LlamaCppProvider
from tests.logging_utils import ModelTestLogger


@dataclass
class UnifiedTestResult:
    """Result from a unified model test."""
    name: str
    category: str
    test_type: str  # "simple" or "complexity"
    complexity_level: int
    prompt: str
    response: str
    success: bool
    latency_ms: float
    prompt_tokens: int
    response_tokens: int
    total_tokens: int
    max_tokens_used: int
    max_tokens_available: int
    found_patterns: List[str]
    missing_patterns: List[str]
    found_unwanted_patterns: List[str]
    latency_per_token: float
    score_wanted_patterns: float
    score_unwanted_patterns: float
    score_looping: float
    error: Optional[str] = None


class UnifiedModelTester:
    """
    Unified model testing framework combining simple and complexity tests.
    
    Creates single log file with comprehensive token information.
    """

    def __init__(self, model_name: str):
        self.model_name = model_name
        self.config = get_model_config()
        self.model_provider = LlamaCppProvider(config = self.config, api_url = LLAMACPP_API_URL)
        self.llm = self.model_provider.get_llm()

        # Create unified logger with datetime format
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        self.logger = ModelTestLogger(model_name, f"test_{timestamp}")
        self.timeout_seconds = 30

    def _calculate_latency_per_token(self, latency_ms: float, response_tokens: int) -> float:
        """Calculate latency per response token."""
        if response_tokens == 0:
            return 0.0
        return round(latency_ms / response_tokens, 2)

    def _calculate_score_wanted_patterns(self, found_patterns: List[str], expected_keywords: List[str]) -> float:
        """Calculate score for wanted patterns (0-1 range)."""
        if not expected_keywords:
            return 1.0
        return round(len(found_patterns) / len(expected_keywords), 2)

    def _calculate_score_unwanted_patterns(self, response_text: str, unwanted_patterns: List[str]) -> float:
        """Calculate score for unwanted patterns (1 = good, 0 = bad)."""
        if not unwanted_patterns:
            return 1.0
        found_unwanted = [pattern for pattern in unwanted_patterns if pattern.lower() in response_text.lower()]
        return round(1.0 - (len(found_unwanted) / len(unwanted_patterns)), 2)

    def _calculate_score_looping(self, response_text: str) -> float:
        """Calculate looping score based on unique words vs total words (1 = no repetition)."""
        if not response_text.strip():
            return 1.0

        words = response_text.split()
        if not words:
            return 1.0

        unique_words = len(set(words))
        total_words = len(words)
        return round((unique_words / total_words), 2)

    def _create_unified_test_cases(self) -> List[Dict[str, Any]]:
        """Create unified test cases combining simple and complexity tests."""
        return [
                # Simple Tests (Levels 1-2)
                {
                        "name"             : "Basic Math",
                        "category"         : "Understanding",
                        "test_type"        : "simple",
                        "complexity_level" : 1,
                        "prompt"           : "What is 15 + 27?",
                        "expected_keywords": ["42"],
                        "unwanted_patterns": ["error", "cannot"],
                        "max_tokens"       : 50
                },
                {
                        "name"             : "Simple Question",
                        "category"         : "Understanding",
                        "test_type"        : "simple",
                        "complexity_level" : 1,
                        "prompt"           : "What is the capital of France?",
                        "expected_keywords": ["Paris"],
                        "unwanted_patterns": ["London", "Berlin"],
                        "max_tokens"       : 50
                },

                # Tool Understanding Tests (Level 2)
                {
                        "name"             : "Time Understanding",
                        "category"         : "Current Date/Time",
                        "test_type"        : "simple",
                        "complexity_level" : 2,
                        "prompt"           : "What time is it right now?",
                        "expected_keywords": ["access", "current"],
                        "unwanted_patterns": ["error", "unknown", ":", "12", "1", "2", "3",
                                              "4", "5", "6", "7", "8", "9", "10", "11"],
                        "max_tokens"       : 50
                },
                {
                        "name"             : "Date Understanding",
                        "category"         : "Current Date/Time",
                        "test_type"        : "simple",
                        "complexity_level" : 2,
                        "prompt"           : "What is today's date?",
                        "expected_keywords": ["access", "current", "date", "today's"],
                        "unwanted_patterns": ["error", "unknown", "2024", "2025", "2023", "january", "february",
                                              "march",
                                              "april", "may", "june", "july", "august", "september", "october",
                                              "november", "december", "/", "-"],
                        "max_tokens"       : 50
                },

                # Reasoning Test (Level 2)
                {
                        "name"             : "Tallest Person Reasoning",
                        "category"         : "Reasoning",
                        "test_type"        : "simple",
                        "complexity_level" : 2,
                        "prompt"           : "Alice is taller than Bob. Charlie is shorter than Alice but taller than Bob. Who is the tallest among Alice, Bob, and Charlie? Explain your answer by comparing their heights.",
                        "expected_keywords": ["Alice", "tallest", "taller", "Bob", "Charlie", "height"],
                        "unwanted_patterns": ["shortest", "smaller", "lowest"],
                        "max_tokens"       : 100
                },

                # Prompt Manager Test (Level 2) - Uses prompt_manager's recommendation
                {
                        "name"             : "Prompt Manager Test",
                        "category"         : "Understanding",
                        "test_type"        : "simple",
                        "complexity_level" : 2,
                        "prompt"           : "Explain the concept of compound interest with 3 examples.",
                        "expected_keywords": ["1.", "2.", "3.", "compound", "interest", "growth", "time", "money",
                                              "rate", "investment"],
                        "unwanted_patterns": ["4. ", "5.", "simple", "basic"],
                        "max_tokens"       : 0  # Use prompt_manager's recommended allocation
                },

                # Financial Tests (Level 3)
                {
                        "name"             : "Basic Finance Term",
                        "category"         : "Finance",
                        "test_type"        : "simple",
                        "complexity_level" : 3,
                        "prompt"           : "What is a stock? Explain in simple terms.",
                        "expected_keywords": ["company", "share", "ownership", "investment", "piece", "stock", "shares",
                                              "stock market", "market"],
                        "unwanted_patterns": ["bond", "cryptocurrency"],
                        "max_tokens"       : 150
                },
                {
                        "name"             : "Interest Calculation 1",
                        "category"         : "Finance",
                        "test_type"        : "simple",
                        "complexity_level" : 3,
                        "prompt"           : "If I invest $1000 at 5% annual interest, how much will I have after 1 year?",
                        "expected_keywords": ["1050", "$1050"],
                        "unwanted_patterns": ["1000", "500"],
                        "max_tokens"       : 100
                },

                # Progressive Complexity Tests (Levels 3-5)
                {
                        "name"             : "Interest Calculation 2",
                        "category"         : "Finance",
                        "test_type"        : "simple",
                        "complexity_level" : 3,
                        "prompt"           : "If I invest $1000 at 5% annual interest, how much will I have after 5 years?",
                        "expected_keywords": ["1276", "$1276", "1276.28", "$1276.28", "$1,276.28"],
                        "unwanted_patterns": ["1000", "1050", "500", "1250", "1250.00"],
                        "max_tokens"       : 100
                },
                {
                        "name"             : "Financial Investment Question",
                        "category"         : "Finance",
                        "test_type"        : "complexity",
                        "complexity_level" : 3,
                        "prompt"           : """A person has $1,000 and wants to invest it. They are 30 years old and want to save for retirement.

What are the basic investment options they should consider? Explain briefly.""",
                        "expected_keywords": ["stocks", "bonds", "retirement", "investment", "diversified", "portfolio",
                                              "ETF", "mutual funds", "index funds", "gold", "bitcoin"],
                        "unwanted_patterns": ["gambling", "lottery", "casino"],
                        "max_tokens"       : 500
                },
                {
                        "name"             : "Multi-step Financial Analysis",
                        "category"         : "Finance",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt"           : """Company ABC has these financial metrics:
- Revenue: $50M (up 10% from last year)
- Net Income: $5M 
- Debt: $15M
- Cash: $8M

The company makes software for small businesses. The software market is growing but competitive.

Questions:
1. Is this company profitable?
2. What is the debt-to-revenue ratio?
3. Does the company seem financially healthy?
4. What might be one risk for this business?

Please answer each question briefly.""",
                        "expected_keywords": ["profitable", "debt", "ratio", "healthy", "risk", "competitive",
                                              "debt-to-revenue", "software"],
                        "unwanted_patterns": ["bankruptcy", "failure", "bankrupt"],
                        "max_tokens"       : 1000
                },
                {
                        "name"             : "Mathematical Trading Question 1",
                        "category"         : "Indicators",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt"           : """Data (Open, High, Low, Close):

                Day 1: 44.34, 45.00, 44.20, 44.09  
                Day 2: 44.09, 45.00, 43.75, 45.00  
                Day 3: 45.00, 45.50, 44.80, 45.34  
                Day 4: 45.34, 46.00, 45.10, 45.50  
                Day 5: 45.50, 46.50, 45.30, 46.00  

                Instructions:  
                1. Calculate and provide the SMA(5) value now.
                2. Explain how did you calculate it.""",
                        "expected_keywords": ["SMA(5)", "45.19", "45.186", "close", "closing",
                                              "44.09 + 45.00 + 45.34 + 45.50 + 46.00"],
                        "unwanted_patterns": ["don't", "know", "access", "Stochastic", "0", "0.0", "45.13", "44.93"],
                        "max_tokens"       : 200
                },
                {
                        "name"             : "Mathematical Trading Question 2",
                        "category"         : "Indicators",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : """Data (Open, High, Low, Close):

                Day 1: 44.34, 45.00, 44.20, 44.09  
                Day 2: 44.09, 45.00, 43.75, 45.00  
                Day 3: 45.00, 45.50, 44.80, 45.34  
                Day 4: 45.34, 46.00, 45.10, 45.50  
                Day 5: 45.50, 46.50, 45.30, 46.00  
                Day 6: 46.00, 46.50, 45.80, 45.90  
                Day 7: 45.90, 46.00, 45.50, 46.20  
                Day 8: 46.20, 46.80, 46.00, 46.70  
                Day 9: 46.70, 47.00, 46.50, 46.90  
                Day 10: 46.90, 47.10, 46.80, 47.10  
                Day 11: 47.10, 47.50, 46.90, 47.40  
                Day 12: 47.40, 47.60, 47.00, 47.00  
                Day 13: 47.00, 47.20, 46.80, 46.80  
                Day 14: 46.80, 47.00, 46.50, 47.10  

                Instructions:  
                1. Calculate and provide the SMA(14) value now.""",
                        "expected_keywords": ["46.22", "46.216", "close", "closing"],
                        "unwanted_patterns": ["don't", "know", "access", "Stochastic", "0", "0.0", "47.10", "46.63"],
                        "max_tokens"       : 350
                },
                {
                        "name"             : "Complex Financial Reasoning",
                        "category"         : "Finance",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : """You are advising someone about their investment portfolio. Here's their situation:

PERSON: Sarah, age 35, software engineer
INCOME: $80,000 per year
SAVINGS: $25,000 in savings account (1% interest)
GOALS: Buy a house in 5 years ($50,000 down payment needed), retire comfortably
RISK TOLERANCE: Moderate (okay with some ups and downs)

CURRENT MARKET: 
- Stock market has been volatile lately
- Interest rates are around 5%
- Housing prices are high but stable

QUESTIONS:
1. How should Sarah allocate her $25,000 across different investments?
2. What percentage should go to stocks vs bonds vs cash?
3. Should she prioritize the house down payment or retirement?
4. What are the main risks she should consider?

Provide specific recommendations with brief reasoning for each.""",
                        "expected_keywords": ["allocate", "stocks", "bonds", "percentage", "house", "retirement",
                                              "risk", "diversified"],
                        "unwanted_patterns": ["gambling", "speculation"],
                        "max_tokens"       : 1000
                },

                # Langgraph Tests
                {
                        "name"             : "LangGraph - Multi-step Logical Deduction",
                        "category"         : "LangGraph",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt"           : "System: You are a logical reasoning expert.\nUser: If all cats are mammals and some mammals are black, can we conclude that some cats are black? Explain your reasoning.",
                        "expected_keywords": ["cats", "mammals", "some", "black", "logical deduction", "conclusion"],
                        "unwanted_patterns": ["dogs", "birds"],
                        "max_tokens"       : 350
                },
                {
                        "name"             : "LangGraph - Advanced Scenario Planning",
                        "category"         : "LangGraph",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : "System: You are a strategic business consultant.\nUser: A company plans to launch a new product in a saturated market. Identify three potential challenges they might face and propose strategies to overcome each challenge.",
                        "expected_keywords": ["market", "challenges", "competition", "competitors", "strategy",
                                              "product launch", "market entry"],
                        "unwanted_patterns": ["easy", "simple"],
                        "max_tokens"       : 900
                },
                {
                        "name": "LangGraph - Expert Multi-factor Analysis PART1 without token limit",
                        "category"         : "LangGraph - Analysis",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : "System: You are an expert financial analyst.\nUser: Evaluate the risks and benefits of investing in emerging markets versus developed markets, considering factors such as volatility, growth potential, and regulatory environment.",
                        "expected_keywords": ["risks", "benefits", "emerging markets", "developed markets",
                                              "volatility", "growth potential", "regulatory environment"],
                        "unwanted_patterns": ["guaranteed", "certain"],
                        "max_tokens"       : 0  # Use prompt_manager's recommended allocation
                },
                {
                        "name": "LangGraph - Expert Multi-factor Analysis PART2 with token limit",
                        "category"         : "LangGraph - Analysis",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : "System: You are an expert financial analyst.\nUser: Evaluate the risks and benefits of investing in emerging markets versus developed markets, considering factors such as volatility, growth potential, and regulatory environment.",
                        "expected_keywords": ["risks", "benefits", "emerging markets", "developed markets",
                                              "volatility", "growth potential", "regulatory environment"],
                        "unwanted_patterns": ["guaranteed", "certain"],
                        "max_tokens"       : 1024
                },
                {
                        "name"             : "Summarization - Short Sentence",
                        "category"         : "LangGraph - Summarization",
                        "test_type"        : "simple",
                        "complexity_level" : 1,
                        "prompt"           : "System: You are a concise summarizer.\nUser: Please summarize the following sentence:\n'The quick brown fox jumps over the lazy dog successfully.'",
                        "expected_keywords": ["fox", "jumps", "dog"],
                        "unwanted_patterns": ["cat", "bird",
                                              "The quick brown fox jumps over the lazy dog successfully."],
                        "max_tokens"       : 100
                },
                {
                        "name"             : "Summarization - Paragraph (Key Points)",
                        "category"         : "LangGraph - Summarization",
                        "test_type"        : "simple",
                        "complexity_level" : 2,
                        "prompt"           : "System: You are an expert at extracting key information.\nUser: Summarize the main points of the following paragraph:\n'Artificial intelligence (AI) is rapidly transforming various industries. Machine learning, a subset of AI, enables systems to learn from data without explicit programming. Deep learning, in turn, is a more advanced form of machine learning that uses neural networks with multiple layers. These technologies are driving innovations from autonomous vehicles to personalized healthcare.'",
                        "expected_keywords": ["AI", "machine learning", "deep learning", "neural networks",
                                              "innovations"],
                        "unwanted_patterns": ["blockchain", "cryptocurrency"],
                        "max_tokens"       : 150
                },
                {
                        "name"             : "Summarization - Short Article (Concise)",
                        "category"         : "LangGraph - Summarization",
                        "test_type"        : "complexity",
                        "complexity_level" : 3,
                        "prompt"           : "System: You are a summarizer. Provide a one-sentence summary of the following article excerpt to reduce its length:\nUser: 'The recent economic downturn has led to significant job losses across multiple sectors. Governments globally are implementing various stimulus packages, including unemployment benefits and direct financial aid, to mitigate the impact. Central banks have also lowered interest rates to encourage borrowing and investment. Despite these efforts, recovery is expected to be gradual, with some economists predicting a prolonged period of adjustment before pre-recession levels are restored. Consumer confidence remains low, affecting retail sales and manufacturing output.'",
                        "expected_keywords": ["economic downturn", "job losses", "stimulus", "recovery", "gradual",
                                              "central banks"],
                        "unwanted_patterns": ["boom", "prosperity"],
                        "max_tokens"       : 200
                },
                {
                        "name"             : "Summarization - Policy Document (Neutral Tone)",
                        "category"         : "LangGraph - Summarization",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt"           : "System: You are an objective and neutral summarizer, focusing on key facts and proposals.\nUser: Summarize the following policy document excerpt, highlighting the proposed changes and their intended outcomes:\n'The proposed environmental policy aims to reduce carbon emissions by 30% by 2035, primarily through stricter regulations on industrial waste and increased investment in renewable energy sources. This initiative also includes subsidies for households adopting solar panels and electric vehicles. The intended outcomes are improved public health due to cleaner air, creation of green jobs, and alignment with international climate agreements. Public feedback sessions are scheduled for the next quarter before final legislative review. Opposition parties have expressed concerns regarding the economic impact on traditional industries.'",
                        "expected_keywords": ["environmental policy", "carbon emissions", "renewable energy",
                                              "subsidies", "public health", "green jobs", "economic impact",
                                              "industrial waste"],
                        "unwanted_patterns": ["fossil fuels", "coal"],
                        "max_tokens"       : 300
                },
                {
                        "name"             : "Summarization - Complex Report (Actionable Insights)",
                        "category"         : "LangGraph - Summarization",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : "System: You are a business analyst. Summarize the following market research report section, focusing on actionable insights and potential implications for a technology company.\nUser: 'The report indicates a significant shift in consumer preferences towards subscription-based software models, with a 25% year-over-year increase in adoption among small to medium enterprises (SMEs). Traditional perpetual license sales have declined by 15% in the same period. Key drivers include lower upfront costs, continuous updates, and flexible scalability. However, customer retention remains a challenge, with a 10% average churn rate after the first year, largely attributed to onboarding complexities and inadequate post-sales support. Competitor A recently introduced a simplified onboarding wizard, which reduced their 6-month churn by 5%.'\n\nWhat are the key trends and what two actionable recommendations can be made for a tech company looking to adapt?",
                        "expected_keywords": ["subscription models", "SMEs", "perpetual license", "churn", "onboarding",
                                              "retention", "actionable", "recommendations", "tech company"],
                        "unwanted_patterns": ["hardware", "physical"],
                        "max_tokens"       : 400
                },
                {
                        "name"             : "Tool Usage - Simple Calculator",
                        "category"         : "LangGraph - Tool Usage",
                        "test_type"        : "simple",
                        "complexity_level" : 1,
                        "prompt"           : "System: You have access to a tool for multiplication. Its function is 'multiply(a: int, b: int)'. Respond with the tool call.\nUser: What is 123 multiplied by 456?",
                        "expected_keywords": ["multiply", "multiply(a=123, b=456)", "multiply(a: 123, b: 456)",
                                              "multiply(a:123, b:456)", "multiply(123, 456)"],
                        "unwanted_patterns": ["addition", "subtract"],
                        "max_tokens"       : 100
                },
                {
                        "name"             : "Tool Usage - Current Weather",
                        "category"         : "LangGraph - Tool Usage",
                        "test_type"        : "complexity",
                        "complexity_level" : 2,
                        "prompt"           : "System: You have access to a 'weather_api' tool. Its function is 'get_current_weather(location: str)'. Respond with the tool call.\nUser: What's the weather like in London right now?",
                        "expected_keywords": ["get_current_weather", "get_current_weather('London')"],
                        "unwanted_patterns": ["1.", "2.", "Paris", "Berlin", "The weather in London is", "degrees",
                                              "temperature", "humidity", "wind", "rain", "cloudy", "sunny"],
                        "max_tokens"       : 100
                },
                {
                        "name"             : "Tool Usage - Stock Price Lookup",
                        "category"         : "LangGraph - Tool Usage",
                        "test_type"        : "complexity",
                        "complexity_level" : 3,
                        "prompt"           : "System: You have access to a 'stock_market_data' tool. It has two functions: 'get_stock_price(ticker_symbol: str)' and 'get_company_info(ticker_symbol: str)'. Respond with the most appropriate tool call to answer the user's request.\nUser: I want to know the current share price of Apple Inc.",
                        "expected_keywords": ["get_company_info", "get_stock_price", "get_stock_price('AAPL')"],
                        "unwanted_patterns": ["1.", "2.", "MSFT", "GOOGL", "Apple", "price", "share"],
                        "max_tokens"       : 150
                },
                {
                        "name"             : "Tool Usage - Complex Search & Summarize",
                        "category"         : "LangGraph - Tool Usage",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt"           : "System: You have access to a 'web_search' tool and a 'summarize' tool. The tools have functions:\n- 'web_search(query: str)'\n- 'summarize(text: str)'\n\nRespond with the correct tool call sequence to fulfill the user's request in correct sequence.\nUser: Can you find recent news about quantum computing breakthroughs from the last 6 months as of 2025 and provide a brief summary?",
                        "expected_keywords": ["web_search(", "summarize(", "summarize(web_search("],
                        "unwanted_patterns": ["1.", "2.", "blockchain", "AI"],
                        "max_tokens"       : 200
                },
                {
                        "name"             : "Tool Usage - Conditional Action (Booking)",
                        "category"         : "LangGraph - Tool Usage",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : "System: You have access to a 'travel_booking' tool. It has functions:\n- 'check_availability(destination: str, date: str, num_guests: int)'\n- 'book_flight(destination: str, date: str, num_guests: int, preference: str)'\n\nRespond with the correct tool call sequence to fulfill the user's request, assuming the user also wants to proceed if available.\nUser: I'd like to book a flight to Tokyo for 2 people on December 24th, 2025. Economy class.",
                        "expected_keywords": ["check_availability(", "check_availability('Tokyo'", "book_flight(",
                                              "book_flight('Tokyo'"],
                        "unwanted_patterns": ["Paris", "business"],
                        "max_tokens"       : 250
                },
                {
                        "name"             : "Memory Chat - Basic Recall",
                        "category"         : "LangGraph - Memory",
                        "test_type"        : "complexity",
                        "complexity_level" : 3,
                        "prompt": "System: You are a helpful assistant that remembers prior messages.\nUser: Hi! My name is Alice.\nAssistant: Hello Alice! How can I assist you today?\nUser: What’s my name?",
                        "expected_keywords": ["Alice", "your name"],
                        "unwanted_patterns": ["Bob", "Charlie"],
                        "max_tokens"       : 150
                },
                {
                        "name"             : "Memory Chat - Multi-turn Context",
                        "category"         : "LangGraph - Memory",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt": "System: You are a helpful AI assistant.\nChat history:\nUser: I work as a software engineer.\nAssistant: Got it.\nUser: What is my profession?",
                        "expected_keywords": ["software engineer"],
                        "unwanted_patterns": ["1.", "2.", "3.", "doctor", "teacher"],
                        "max_tokens"       : 50
                },
                {
                        "name"             : "Memory Chat - Reference Past Preferences",
                        "category"         : "LangGraph - Memory",
                        "test_type"        : "complexity",
                        "complexity_level" : 4,
                        "prompt": "System: You are a helpful AI assistant.\nChat history:\nUser: Last time we chatted, I said I prefer short and direct answers.\nAssistant: Understood, I will keep answers brief.\nUser: Can you summarize the top 5 benefits of meditation?",
                        "expected_keywords": ["brief", "short", "benefits", "meditation"],
                        "unwanted_patterns": ["6.", "7.", "long", "detailed"],
                        "max_tokens"       : 150
                },
                {
                        "name"             : "Memory Chat + Tool Call - Lookup Past Conversations",
                        "category"         : "LangGraph - Memory",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : (
                                "System: You are a helpful AI assistant.\n"
                                "You have access to the following tool:\n"
                                "- memory_search(keyword: str): Searches the user's long-term memory for relevant information matching the keyword.\n"
                                "When asked to recall past conversations, respond only by calling this tool with the required parameters in the following format:\n"
                                '{ "tool": "memory_search", "args": { "keyword": "<keyword>" } }\n'
                                "For example:\n"
                                '{ "tool": "memory_search", "args": { "keyword": "favorite hobby" } }\n'
                                "User: Recall what we discussed about my favorite hobby last month."
                        ),
                        "expected_keywords": ["memory_search", "{ \"tool\": \"memory_search\"",
                                              "{ \"tool\": \"memory_search\", \"args\": { \"keyword\":"],
                        "unwanted_patterns": ["1.", "2.", "3.", "error", "cannot", "I remember"],
                        "max_tokens"       : 200
                },
                {
                        "name"             : "Memory Chat + Tool Call - Lookup Past Conversations (Strict Tool Call Required)",
                        "category"         : "LangGraph - Memory",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : (
                                "System: You are a helpful AI assistant.\n"
                                "You have access to the following tool:\n"
                                "- memory_search(query: str): Queries the user's long-term memory.\n"
                                "When asked to recall past conversations, do NOT answer directly.\n"
                                "Respond ONLY with the exact tool call using the format:\n"
                                'memory_search(query: "<query>")\n\n'
                                "User: Recall what we discussed about my favorite hobby last month."
                        ),
                        "expected_keywords": ["memory_search", "memory_search(", "memory_search(\"",
                                              "memory_search(query: "],
                        "unwanted_patterns": ["1.", "2.", "3.", "error", "cannot"],
                        "max_tokens"       : 50
                },
                {
                        "name"             : "Memory Chat + Tool Call - Update User Profile",
                        "category"         : "LangGraph - Memory",
                        "test_type"        : "complexity",
                        "complexity_level" : 5,
                        "prompt"           : """System: You are a helpful AI assistant that manages user profiles.\nYou have access to the following tool:\n- profile_update(preferred_food: str, activity: str): Updates the user's profile with their preferred food and activity.\nWhen you need to update the user's profile, respond only by calling this tool with the required parameters in the following format:\n{ "tool": "profile_update", "args": { "preferred_food": "<preferred_food>", "activity": "<activity>" } }\nFor example:\n{ "tool": "profile_update", "args": { "preferred_food": "carnivorous", "activity": "hiking" } }\nOtherwise, answer the user directly.\n\nUser: Please update my profile to say I now prefer vegan food and morning workouts.""",
                        "expected_keywords": ["profile_update", "{ \"tool\": \"profile_update\"",
                                              "{ \"tool\": \"profile_update\", \"args\": ",
                                              "{ \"tool\": \"profile_update\", \"args\": { \"preferred_food\": \"vegan\", \"activity\": \"morning workouts\" } }"],
                        "unwanted_patterns": ["1.", "2.", "3.", "error", "cannot"],
                        "max_tokens"       : 350
                }

        ]

    def _get_dynamic_token_info(self, prompt: str, requested_max_tokens: int) -> Dict[str, int]:
        """
        Get token allocation information using prompt_manager's adjust_max_tokens.

        If requested_max_tokens=0, use prompt_manager's recommended allocation.
        """
        # Use prompt_manager's count_tokens method for consistency
        if hasattr(self.llm, 'prompt_manager'):
            prompt_tokens = self.llm.prompt_manager.count_tokens(prompt)
        else:
            prompt_tokens = self.logger.count_tokens(prompt)

        # Always use prompt_manager for proper token evaluation
        if hasattr(self.llm, 'prompt_manager'):
            # Calculate maximum available tokens in context window
            max_context = self.llm.prompt_manager.max_context_tokens
            reserved = self.llm.prompt_manager.reserved_tokens
            max_available_tokens = max_context - prompt_tokens - reserved

            if requested_max_tokens == 0:
                # Use prompt_manager's full recommendation (all available tokens)
                actual_requested = max_available_tokens
                used_tokens = self.llm.prompt_manager.adjust_max_tokens(prompt, max_available_tokens)
            else:
                # Use the requested max_tokens but limit by available space
                actual_requested = requested_max_tokens
                used_tokens = self.llm.prompt_manager.adjust_max_tokens(prompt, requested_max_tokens)
        else:
            # Fallback if prompt_manager not available (shouldn't happen)
            print("⚠️ Warning: prompt_manager not found, using fallback calculation")
            max_context = 4096
            reserved = 50
            max_available_tokens = max_context - prompt_tokens - reserved

            if requested_max_tokens == 0:
                actual_requested = max_available_tokens
            else:
                actual_requested = requested_max_tokens
            used_tokens = min(actual_requested, max_available_tokens)

        return {
                "prompt_tokens"       : prompt_tokens,
                "max_tokens_requested": actual_requested,
                "max_tokens_available": max_available_tokens,  # Always show full available space
                "max_tokens_used"     : used_tokens,  # What we actually use (limited by request or availability)
                "prompt_manager_used" : hasattr(self.llm, 'prompt_manager')
        }

    def _run_single_test(self, test_case: Dict[str, Any]) -> UnifiedTestResult:
        """Run a single test case and return the result."""
        self.logger.log_test_start(test_case["name"], test_case["category"], test_case["complexity_level"])

        try:
            # Get token information
            token_info = self._get_dynamic_token_info(test_case["prompt"], test_case.get("max_tokens", 200))

            # Set max_tokens for this test
            original_max_tokens = self.llm.max_tokens
            self.llm.max_tokens = token_info["max_tokens_used"]

            # Prepare enhanced LLM parameters for logging
            llm_params = {
                    "max_tokens_requested": token_info["max_tokens_requested"],
                    "max_tokens_available": token_info["max_tokens_available"],
                    "max_tokens_used"     : token_info["max_tokens_used"],
                    "prompt_tokens"       : token_info["prompt_tokens"],
                    "prompt_manager_used" : token_info["prompt_manager_used"],
                    "was_auto_tokens"     : test_case.get("max_tokens", 0) == 0,  # Track if this was auto-allocated
                    "temperature": getattr(self.llm, 'temperature', 'N/A'),
                    "model_provider"      : "llamacpp",
                    "api_url"             : getattr(self.llm, 'api_url', 'unknown')
            }

            # Log prompt with enhanced token parameters
            self.logger.log_prompt(test_case["prompt"], test_case["name"], llm_params)

            start_time = time.time()

            # Execute the test
            response = self.llm.invoke(test_case["prompt"])

            latency_ms = (time.time() - start_time) * 1000

            # Restore original max_tokens
            self.llm.max_tokens = original_max_tokens

            # Extract response text
            if hasattr(response, 'content'):
                response_text = response.content.strip()
            elif hasattr(response, 'text'):
                response_text = response.text.strip()
            else:
                response_text = str(response).strip()

            # Calculate response tokens using prompt_manager for consistency
            if hasattr(self.llm, 'prompt_manager'):
                response_tokens = self.llm.prompt_manager.count_tokens(response_text)
            else:
                response_tokens = self.logger.count_tokens(response_text)

            # Check for success based on expected keywords
            expected_keywords = test_case.get("expected_keywords", [])
            found_patterns = [kw for kw in expected_keywords if kw.lower() in response_text.lower()]
            missing_patterns = [kw for kw in expected_keywords if kw.lower() not in response_text.lower()]
            success = len(found_patterns) > 0

            # Check for unwanted patterns
            unwanted_patterns = test_case.get("unwanted_patterns", [])
            found_unwanted_patterns = [pattern for pattern in unwanted_patterns if
                                       pattern.lower() in response_text.lower()]

            # Calculate new measurements
            latency_per_token = self._calculate_latency_per_token(latency_ms, response_tokens)
            score_wanted_patterns = self._calculate_score_wanted_patterns(found_patterns, expected_keywords)
            score_unwanted_patterns = self._calculate_score_unwanted_patterns(response_text,
                                                                              unwanted_patterns) if success else None
            score_looping = self._calculate_score_looping(response_text) if success else None

            # Log response with unified test results
            self.logger.log_response(
                    response_text, latency_ms, test_case["name"],
                    success = success, found_patterns = found_patterns,
                    missing_patterns = missing_patterns, llm_params = llm_params
            )

            result = UnifiedTestResult(
                    name = test_case["name"],
                    category = test_case["category"],
                    test_type = test_case["test_type"],
                    complexity_level = test_case["complexity_level"],
                    prompt = test_case["prompt"],
                    response = response_text,
                    success = success,
                    latency_ms = latency_ms,
                    prompt_tokens = token_info["prompt_tokens"],
                    response_tokens = response_tokens,
                    total_tokens = token_info["prompt_tokens"] + response_tokens,
                    max_tokens_used = token_info["max_tokens_used"],
                    max_tokens_available = token_info["max_tokens_available"],
                    found_patterns = found_patterns,
                    missing_patterns = missing_patterns,
                    found_unwanted_patterns = found_unwanted_patterns,
                    latency_per_token = latency_per_token,
                    score_wanted_patterns = score_wanted_patterns,
                    score_unwanted_patterns = score_unwanted_patterns  if success else None,
                    score_looping = score_looping  if success else None
            )

            # Build the base result_data dictionary with unconditional fields
            result_data = {
                    "name"                   : test_case["name"],
                    "category"               : test_case["category"],
                    "test_type"              : test_case["test_type"],
                    "complexity_level"       : test_case["complexity_level"],
                    "llm_params"             : llm_params,
                    "success"                : success,
                    "latency_ms"             : latency_ms,
                    "latency_per_token"      : latency_per_token,
                    "prompt_tokens"          : token_info["prompt_tokens"],
                    "response_tokens"        : response_tokens,
                    "total_tokens"           : token_info["prompt_tokens"] + response_tokens,
                    "max_tokens_requested"   : token_info["max_tokens_requested"],
                    "max_tokens_available"   : token_info["max_tokens_available"],
                    "max_tokens_used"        : token_info["max_tokens_used"],
                    "prompt_manager_used"    : token_info["prompt_manager_used"],
                    "found_patterns"         : found_patterns,
                    "missing_patterns"       : missing_patterns,
                    "found_unwanted_patterns": found_unwanted_patterns,
                    "score_wanted_patterns"  : score_wanted_patterns,
                    "score_unwanted_patterns": score_unwanted_patterns,
                    "score_looping"          : score_looping
            }
            self.logger.store_test_result(result_data)

            return result

        except Exception as e:
            error_msg = str(e)
            self.logger.log_test_error(test_case["name"], error_msg)

            return UnifiedTestResult(
                    name = test_case["name"],
                    category = test_case["category"],
                    test_type = test_case["test_type"],
                    complexity_level = test_case["complexity_level"],
                    prompt = test_case["prompt"],
                    response = "",
                    success = False,
                    latency_ms = 0,
                    prompt_tokens = 0,
                    response_tokens = 0,
                    total_tokens = 0,
                    max_tokens_used = 0,
                    max_tokens_available = 0,
                    found_patterns = [],
                    missing_patterns = test_case.get("expected_keywords", []),
                    found_unwanted_patterns = [],
                    latency_per_token = 0.0,
                    score_wanted_patterns = 0.0,
                    score_unwanted_patterns = 0.0,
                    score_looping = 0.0,
                    error = error_msg
            )

    def run_unified_tests(self) -> List[UnifiedTestResult]:
        """Run all unified model tests."""
        print(f"🧪 Running Unified Model Tests for: {self.model_name}")
        print("=" * 70)

        test_cases = self._create_unified_test_cases()
        results = []

        for i, test_case in enumerate(test_cases, 1):
            print(
                    f"\n[{i}/{len(test_cases)}] Testing: {test_case['name']} ({test_case['category']}, Level {test_case['complexity_level']})")
            print(f"Type: {test_case['test_type'].title()}")
            print(f"Prompt: {test_case['prompt'][:80]}{'...' if len(test_case['prompt']) > 80 else ''}")

            result = self._run_single_test(test_case)
            results.append(result)

            if result.success:
                print(f"✅ PASSED ({result.latency_ms:.0f}ms)")
                print(
                        f"Tokens: {result.prompt_tokens}→{result.response_tokens} (used {result.max_tokens_used}/{result.max_tokens_available})")
                print(f"Response: {result.response[:100]}{'...' if len(result.response) > 100 else ''}")
            else:
                print(f"❌ FAILED ({result.latency_ms:.0f}ms)")
                if result.error:
                    print(f"Error: {result.error}")
                else:
                    print(f"Response: {result.response[:100]}{'...' if len(result.response) > 100 else ''}")

        self._print_summary(results)
        self._save_results(results)

        return results

    def _print_summary(self, results: List[UnifiedTestResult]):
        """Print comprehensive test summary."""
        print("\n" + "=" * 70)
        print("📊 UNIFIED TEST SUMMARY")
        print("=" * 70)

        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.success)
        failed_tests = total_tests - passed_tests

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")

        # Category breakdown
        categories = {}
        for result in results:
            if result.category not in categories:
                categories[result.category] = {"total": 0, "passed": 0}
            categories[result.category]["total"] += 1
            if result.success:
                categories[result.category]["passed"] += 1

        print("\n📈 CATEGORY BREAKDOWN:")
        for category, stats in categories.items():
            success_rate = (stats["passed"] / stats["total"]) * 100
            print(f"  {category}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")

        # Test type breakdown
        test_types = {}
        for result in results:
            if result.test_type not in test_types:
                test_types[result.test_type] = {"total": 0, "passed": 0}
            test_types[result.test_type]["total"] += 1
            if result.success:
                test_types[result.test_type]["passed"] += 1

        print("\n🔬 TEST TYPE BREAKDOWN:")
        for test_type, stats in test_types.items():
            success_rate = (stats["passed"] / stats["total"]) * 100
            print(f"  {test_type.title()}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")

        # Complexity level breakdown
        complexity_levels = {}
        for result in results:
            level = result.complexity_level
            if level not in complexity_levels:
                complexity_levels[level] = {"total": 0, "passed": 0}
            complexity_levels[level]["total"] += 1
            if result.success:
                complexity_levels[level]["passed"] += 1

        print("\n📊 COMPLEXITY LEVEL BREAKDOWN:")
        for level in sorted(complexity_levels.keys()):
            stats = complexity_levels[level]
            success_rate = (stats["passed"] / stats["total"]) * 100
            print(f"  Level {level}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")

        # Token usage analysis
        successful_results = [r for r in results if r.success and r.latency_ms > 0]
        if successful_results:
            avg_latency = sum(r.latency_ms for r in successful_results) / len(successful_results)
            avg_prompt_tokens = sum(r.prompt_tokens for r in successful_results) / len(successful_results)
            avg_response_tokens = sum(r.response_tokens for r in successful_results) / len(successful_results)
            avg_total_tokens = sum(r.total_tokens for r in successful_results) / len(successful_results)

            print(f"\n⚡ PERFORMANCE METRICS:")
            print(f"  Average Latency: {avg_latency:.0f}ms")
            print(f"  Average Prompt Tokens: {avg_prompt_tokens:.1f}")
            print(f"  Average Response Tokens: {avg_response_tokens:.1f}")
            print(f"  Average Total Tokens: {avg_total_tokens:.1f}")

    def _save_results(self, results: List[UnifiedTestResult]):
        """Save test results using the unified logging system."""
        # Calculate summary data
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.success)

        # Update session summary
        summary_data = {
                "model_name"  : self.model_name,
                "test_type"   : "unified_test",
                "total_tests" : total_tests,
                "passed_tests": passed_tests,
                "success_rate": (passed_tests / total_tests) if total_tests > 0 else 0.0,
                "session_end" : time.strftime("%Y-%m-%d %H:%M:%S")
        }

        # Update the logger's session data summary
        self.logger.session_data["summary"] = summary_data

        # Save using the logging system
        self.logger.save_json_data()
        print(f"\n💾 Results saved to: {self.logger.json_filepath}")


def run_unified_model_test(model_name: str = None):
    """
    Run unified model test with the specified model.

    Args:
        model_name: Name of the model being tested (will prompt if None)
    """
    if model_name is None:
        model_name = input(
                "Enter model name to test: ").strip() or "D_openblas-qwen3-coder-30b-a3b-instruct-q4_k_m"

    print(f"\n🚀 Starting Unified Model Test for: {model_name}")

    tester = UnifiedModelTester(model_name)
    results = tester.run_unified_tests()

    return results


if __name__ == "__main__":
    try:
        run_unified_model_test()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
