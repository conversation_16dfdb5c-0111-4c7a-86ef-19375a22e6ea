#!/usr/bin/env python3
"""
Context Length Verification Test

This test verifies the actual context length of the remote LLM by:
1. Fetching server properties via /props endpoint
2. Generating prompts with incremental numbers of varying lengths
3. Testing the model's ability to see numbers at different token positions
4. Determining the practical context limit

Usage:
    python tests/test_context_length_verification.py
"""

import json
import os
import sys
from typing import Dict, Any, Optional

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llamacpp_provider import LlamaCppProvider
from models.prompt_manager import PromptManager
from utils.model_info import get_server_info
from logger.get_logger import get_logger
import config


def generate_incremental_numbers_prompt(target_tokens: int = 2000, start_number: int = 1) -> tuple[str, int]:
    """
    Generate a prompt with incremental numbers to test context length.
    
    Args:
        target_tokens: Target number of tokens for the prompt
        start_number: Starting number for the sequence
        
    Returns:
        Tuple of (prompt_string, actual_token_count)
    """
    logger = get_logger()

    # Initialize prompt manager for accurate token counting
    prompt_manager = PromptManager(max_context_tokens = config.MAX_CONTEXT_TOKENS,
                                   reserved_tokens = config.RESERVED_TOKENS)

    # Starting question
    base_prompt = "What is the highest number you can see in this list?\n\nNumbers: "

    # Build the number list
    numbers = []
    current_prompt = base_prompt

    number = start_number
    while True:
        # Test adding the next number
        test_numbers = numbers + [str(number)]
        test_prompt = base_prompt + " ".join(test_numbers) + "\n\nAnswer:"

        # Count tokens accurately
        token_count = prompt_manager.count_tokens(test_prompt)

        if token_count >= target_tokens:
            break

        numbers.append(str(number))
        current_prompt = test_prompt
        number += 1

    final_prompt = base_prompt + " ".join(numbers) + "\n\nAnswer:"
    final_token_count = prompt_manager.count_tokens(final_prompt)

    logger.info(f"Generated prompt with {len(numbers)} numbers (from {start_number} to {number - 1})")
    logger.info(f"Final token count: {final_token_count}")
    logger.info(f"Highest number in sequence: {number - 1}")

    return final_prompt, final_token_count


def test_context_length_with_numbers(target_tokens: int) -> Dict[str, Any]:
    """
    Test context length by sending a prompt with incremental numbers.
    
    Args:
        target_tokens: Target number of tokens to test
        
    Returns:
        Dictionary with test results
    """
    logger = get_logger()

    logger.info(f"Testing context length with {target_tokens} token prompt")

    # Generate the test prompt
    prompt, actual_tokens = generate_incremental_numbers_prompt(target_tokens)

    # Create LLM instance
    provider = LlamaCppProvider(config = config.get_model_config())
    llm = provider.get_llm()

    # Log the prompt details
    logger.debug(f"Test prompt (first 200 chars): {prompt[:200]}...")
    logger.debug(f"Test prompt (last 200 chars): ...{prompt[-200:]}")
    logger.debug(f"Actual token count: {actual_tokens}")

    try:
        # Send the prompt to the model
        from langchain.schema.messages import HumanMessage

        messages = [HumanMessage(content = prompt)]

        logger.info(f"Sending {actual_tokens}-token prompt to LLM...")
        result = llm._generate(messages)

        response = result.generations[0].message.content
        logger.info(f"LLM Response: {response}")

        # Try to extract the highest number from the response
        highest_number_seen = extract_highest_number_from_response(response)

        return {
                "success"            : True,
                "target_tokens"      : target_tokens,
                "actual_tokens"      : actual_tokens,
                "response"           : response,
                "highest_number_seen": highest_number_seen,
                "error"              : None
        }

    except Exception as e:
        logger.error(f"Error testing {target_tokens} tokens: {str(e)}")
        return {
                "success"            : False,
                "target_tokens"      : target_tokens,
                "actual_tokens"      : actual_tokens,
                "response"           : None,
                "highest_number_seen": None,
                "error"              : str(e)
        }


def extract_highest_number_from_response(response: str) -> Optional[int]:
    """
    Extract the highest number mentioned in the LLM response.
    
    Args:
        response: LLM response text
        
    Returns:
        Highest number found or None
    """
    import re

    # Find all numbers in the response
    numbers = re.findall(r'\b\d+\b', response)

    if not numbers:
        return None

    # Convert to integers and find the maximum
    try:
        int_numbers = [int(n) for n in numbers]
        return max(int_numbers)
    except ValueError:
        return None


def run_context_verification_tests():
    """
    Run a series of context length verification tests.
    """
    logger = get_logger()

    print("=" * 80)
    print("CONTEXT LENGTH VERIFICATION TEST")
    print("=" * 80)

    # First, get server information
    print("\n1. Fetching server information...")
    server_info = get_server_info()

    print(f"   Server available: {server_info['server_available']}")
    if server_info['server_available']:
        print(f"   Model name: {server_info['model_name']}")
        print(f"   Reported context length: {server_info['context_length']}")

        # Show raw props for debugging
        if server_info['props']:
            print(f"   Raw server props: {json.dumps(server_info['props'], indent = 2)}")

    # Test different token lengths
    test_lengths = [500, 1000, 1500, 2000, 2500]

    print(f"\n2. Testing context lengths with incremental number prompts...")
    print(f"   Config MAX_CONTEXT_TOKENS: {config.MAX_CONTEXT_TOKENS}")
    print(f"   Config RESERVED_TOKENS: {config.RESERVED_TOKENS}")

    results = []

    for target_tokens in test_lengths:
        print(f"\n   Testing {target_tokens} tokens...")
        result = test_context_length_with_numbers(target_tokens)
        results.append(result)

        if result["success"]:
            print(f"   ✅ SUCCESS: Model responded to {result['actual_tokens']} token prompt")
            print(f"      Highest number seen: {result['highest_number_seen']}")
            print(f"      Response preview: {result['response'][:100]}...")
        else:
            print(f"   ❌ FAILED: {result['error']}")
            break  # Stop testing higher lengths if we hit a failure

    # Summary
    print(f"\n3. Test Summary:")
    print("   " + "-" * 60)

    successful_tests = [r for r in results if r["success"]]
    if successful_tests:
        max_successful = max(successful_tests, key = lambda x: x["actual_tokens"])
        print(f"   Maximum successful token count: {max_successful['actual_tokens']}")
        print(f"   Highest number model could see: {max_successful['highest_number_seen']}")

        # Compare with server reported values
        if server_info['context_length']:
            reported_ctx = server_info['context_length']
            actual_ctx = max_successful['actual_tokens']
            print(f"   Server reported context: {reported_ctx}")
            print(f"   Practical context limit: {actual_ctx}")

            if actual_ctx > reported_ctx:
                print(f"   🎉 GOOD NEWS: Actual context ({actual_ctx}) > reported ({reported_ctx})")
            elif actual_ctx < reported_ctx:
                print(f"   ⚠️  WARNING: Actual context ({actual_ctx}) < reported ({reported_ctx})")
            else:
                print(f"   ✅ MATCH: Actual context matches reported context")
    else:
        print("   ❌ No successful tests - all prompts failed")

    return results


if __name__ == "__main__":
    # Set up logging
    import logging

    logging.basicConfig(level = logging.INFO, format = '%(asctime)s - %(levelname)s - %(message)s')

    try:
        results = run_context_verification_tests()

        # Save results to file for analysis
        results_file = "context_verification_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                    "timestamp"         : str(os.popen('date').read().strip()),
                    "config_max_context": config.MAX_CONTEXT_TOKENS,
                    "config_reserved"   : config.RESERVED_TOKENS,
                    "server_info"       : get_server_info(),
                    "test_results"      : results
            }, f, indent = 2)

        print(f"\n📄 Detailed results saved to: {results_file}")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
