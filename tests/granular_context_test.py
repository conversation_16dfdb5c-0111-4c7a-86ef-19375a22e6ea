#!/usr/bin/env python3
"""
Granular Context Test - Find exact context truncation point
"""

import sys
import os
import json

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llamacpp_provider import LlamaCppProvider
from models.prompt_manager import PromptManager
from logger.get_logger import get_logger
import config


def test_specific_token_count(target_tokens: int):
    """Test a specific token count and return detailed results."""
    logger = get_logger()
    
    # Initialize prompt manager
    prompt_manager = PromptManager(max_context_tokens=config.MAX_CONTEXT_TOKENS, 
                                   reserved_tokens=config.RESERVED_TOKENS)
    
    # Generate the test prompt
    base_prompt = "What is the highest number you can see in this list?\n\nNumbers: "
    
    numbers = []
    number = 1
    while True:
        test_numbers = numbers + [str(number)]
        test_prompt = base_prompt + " ".join(test_numbers) + "\n\nAnswer:"
        token_count = prompt_manager.count_tokens(test_prompt)
        
        if token_count >= target_tokens:
            break
            
        numbers.append(str(number))
        number += 1
    
    final_prompt = base_prompt + " ".join(numbers) + "\n\nAnswer:"
    final_token_count = prompt_manager.count_tokens(final_prompt)
    highest_number_in_sequence = number - 1
    
    # Send to model
    provider = LlamaCppProvider(config=config.get_model_config())
    llm = provider.get_llm()
    
    try:
        from langchain.schema.messages import HumanMessage
        messages = [HumanMessage(content=final_prompt)]
        
        result = llm._generate(messages)
        response = result.generations[0].message.content.strip()
        
        # Extract numbers from response
        import re
        numbers_in_response = re.findall(r'\b\d+\b', response)
        
        if numbers_in_response:
            highest_response_number = max(int(n) for n in numbers_in_response)
        else:
            highest_response_number = None
        
        # Calculate what percentage of the sequence the model saw
        if highest_response_number and highest_number_in_sequence > 0:
            visibility_percentage = (highest_response_number / highest_number_in_sequence) * 100
        else:
            visibility_percentage = 0
        
        return {
            "target_tokens": target_tokens,
            "actual_tokens": final_token_count,
            "highest_number_in_sequence": highest_number_in_sequence,
            "model_response": response,
            "highest_response_number": highest_response_number,
            "visibility_percentage": round(visibility_percentage, 1),
            "success": True,
            "truncated": highest_response_number < highest_number_in_sequence if highest_response_number else True
        }
        
    except Exception as e:
        return {
            "target_tokens": target_tokens,
            "actual_tokens": final_token_count,
            "highest_number_in_sequence": highest_number_in_sequence,
            "model_response": None,
            "highest_response_number": None,
            "visibility_percentage": 0,
            "success": False,
            "truncated": True,
            "error": str(e)
        }


def run_granular_tests():
    """Run granular tests to find exact truncation point."""
    
    print("🔬 GRANULAR CONTEXT TRUNCATION TEST")
    print("=" * 80)
    print("Testing token ranges between 500-1000 to find exact truncation point...")
    print()
    
    # Test incremental token counts
    test_cases = [
        500, 550, 600, 650, 700, 750, 800, 850, 900, 950, 1000,
        1050, 1100, 1150, 1200  # A few beyond 1000 to confirm pattern
    ]
    
    results = []
    
    for tokens in test_cases:
        print(f"🧪 Testing {tokens} tokens...")
        result = test_specific_token_count(tokens)
        results.append(result)
        
        if result["success"]:
            status = "🔴 TRUNCATED" if result["truncated"] else "✅ COMPLETE"
            print(f"   {status}: Sequence 1-{result['highest_number_in_sequence']}, "
                  f"Model saw up to {result['highest_response_number']}, "
                  f"Visibility: {result['visibility_percentage']}%")
        else:
            print(f"   ❌ FAILED: {result.get('error', 'Unknown error')}")
            break
        
        # If we're getting very low visibility, we've found the problem area
        if result["success"] and result["visibility_percentage"] < 50:
            print(f"   ⚠️  Low visibility detected - context truncation is severe")
    
    # Analysis
    print(f"\n📊 ANALYSIS:")
    print("=" * 60)
    
    successful_results = [r for r in results if r["success"]]
    
    if successful_results:
        # Find the last test with 100% visibility
        full_visibility = [r for r in successful_results if r["visibility_percentage"] >= 99]
        if full_visibility:
            last_full = full_visibility[-1]
            print(f"✅ Last complete visibility: {last_full['target_tokens']} tokens")
            print(f"   Sequence: 1 to {last_full['highest_number_in_sequence']}")
        
        # Find first test with significant truncation
        truncated = [r for r in successful_results if r["visibility_percentage"] < 90]
        if truncated:
            first_truncated = truncated[0]
            print(f"🔴 First significant truncation: {first_truncated['target_tokens']} tokens")
            print(f"   Visibility dropped to: {first_truncated['visibility_percentage']}%")
        
        # Show visibility trend
        print(f"\n📈 Visibility Trend:")
        for result in successful_results:
            status_icon = "✅" if result["visibility_percentage"] >= 99 else "🔴"
            print(f"   {status_icon} {result['target_tokens']:4d} tokens → "
                  f"{result['visibility_percentage']:5.1f}% visibility "
                  f"(saw up to {result['highest_response_number']})")
        
        # Estimate practical limit
        practical_limits = [r for r in successful_results if r["visibility_percentage"] >= 95]
        if practical_limits:
            practical_limit = practical_limits[-1]["target_tokens"]
            print(f"\n🎯 PRACTICAL TOKEN LIMIT: ~{practical_limit} tokens")
            print(f"   (Last test with >95% sequence visibility)")
        
        # Compare with server reported limit
        print(f"\n🔍 COMPARISON:")
        print(f"   Server reported n_ctx: 1024 tokens")
        print(f"   Config MAX_CONTEXT_TOKENS: {config.MAX_CONTEXT_TOKENS} tokens")
        if practical_limits:
            print(f"   Actual practical limit: ~{practical_limit} tokens")
            overhead = 1024 - practical_limit
            print(f"   Context overhead: ~{overhead} tokens")
    
    # Save detailed results
    results_file = "granular_context_results.json"
    with open(results_file, 'w') as f:
        json.dump({
            "timestamp": str(os.popen('date').read().strip()),
            "test_description": "Granular context truncation test between 500-1200 tokens",
            "server_reported_ctx": 1024,
            "config_max_context": config.MAX_CONTEXT_TOKENS,
            "test_results": results
        }, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return results


if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        results = run_granular_tests()
        
        # Quick summary
        successful = [r for r in results if r["success"]]
        if successful:
            print(f"\n🎯 QUICK SUMMARY:")
            print(f"   Tests completed: {len(successful)}")
            full_visibility = [r for r in successful if r["visibility_percentage"] >= 99]
            if full_visibility:
                print(f"   Safe token limit: {full_visibility[-1]['target_tokens']} tokens")
            else:
                print(f"   ⚠️  No tests achieved full visibility - context limit exceeded")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
