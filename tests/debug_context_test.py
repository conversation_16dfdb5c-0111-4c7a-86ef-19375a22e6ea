#!/usr/bin/env python3
"""
Debug Context Test - Verify what the model actually sees
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llamacpp_provider import LlamaCppProvider
from models.prompt_manager import PromptManager
from logger.get_logger import get_logger
import config


def debug_specific_token_count(target_tokens: int):
    """Debug what happens with a specific token count."""
    logger = get_logger()
    
    print(f"\n🔍 DEBUGGING {target_tokens} TOKEN TEST")
    print("=" * 60)
    
    # Initialize prompt manager
    prompt_manager = PromptManager(max_context_tokens=config.MAX_CONTEXT_TOKENS, 
                                   reserved_tokens=config.RESERVED_TOKENS)
    
    # Generate the test prompt
    base_prompt = "What is the highest number you can see in this list?\n\nNumbers: "
    
    numbers = []
    number = 1
    while True:
        test_numbers = numbers + [str(number)]
        test_prompt = base_prompt + " ".join(test_numbers) + "\n\nAnswer:"
        token_count = prompt_manager.count_tokens(test_prompt)
        
        if token_count >= target_tokens:
            break
            
        numbers.append(str(number))
        number += 1
    
    final_prompt = base_prompt + " ".join(numbers) + "\n\nAnswer:"
    final_token_count = prompt_manager.count_tokens(final_prompt)
    
    print(f"📊 Prompt Statistics:")
    print(f"   Target tokens: {target_tokens}")
    print(f"   Actual tokens: {final_token_count}")
    print(f"   Numbers generated: {len(numbers)} (from 1 to {number-1})")
    print(f"   Highest number in sequence: {number-1}")
    
    # Show prompt structure
    print(f"\n📝 Prompt Structure:")
    print(f"   Base question: {len(base_prompt)} chars")
    print(f"   Numbers section: {len(' '.join(numbers))} chars")
    print(f"   Total prompt: {len(final_prompt)} chars")
    
    # Show first and last numbers
    first_10 = numbers[:10]
    last_10 = numbers[-10:]
    print(f"   First 10 numbers: {' '.join(first_10)}")
    print(f"   Last 10 numbers: {' '.join(last_10)}")
    
    # Show prompt preview
    print(f"\n📄 Prompt Preview:")
    print(f"   First 100 chars: {final_prompt[:100]}")
    print(f"   Last 100 chars: {final_prompt[-100:]}")
    
    # Send to model
    provider = LlamaCppProvider(config=config.get_model_config())
    llm = provider.get_llm()
    
    try:
        from langchain.schema.messages import HumanMessage
        messages = [HumanMessage(content=final_prompt)]
        
        print(f"\n🤖 Sending to LLM...")
        result = llm._generate(messages)
        response = result.generations[0].message.content
        
        print(f"📤 LLM Response:")
        print(f"   Full response: {response}")
        
        # Try to extract numbers from response
        import re
        numbers_in_response = re.findall(r'\b\d+\b', response)
        print(f"   Numbers found in response: {numbers_in_response}")
        
        if numbers_in_response:
            max_number = max(int(n) for n in numbers_in_response)
            print(f"   Highest number in response: {max_number}")
            
            # Check if this number was actually in our sequence
            if max_number <= (number-1):
                print(f"   ✅ Number {max_number} was in our sequence (valid)")
            else:
                print(f"   ❌ Number {max_number} was NOT in our sequence (hallucination)")
        
        return {
            "target_tokens": target_tokens,
            "actual_tokens": final_token_count,
            "highest_number_in_sequence": number-1,
            "response": response,
            "numbers_in_response": numbers_in_response,
            "success": True
        }
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return {
            "target_tokens": target_tokens,
            "actual_tokens": final_token_count,
            "highest_number_in_sequence": number-1,
            "response": None,
            "numbers_in_response": None,
            "success": False,
            "error": str(e)
        }


def run_debug_tests():
    """Run debug tests for specific token counts."""
    
    print("🔬 CONTEXT DEBUG VERIFICATION")
    print("=" * 80)
    
    # Test the suspicious cases
    test_cases = [500, 1000, 1200]
    
    results = []
    for tokens in test_cases:
        result = debug_specific_token_count(tokens)
        results.append(result)
        
        if not result["success"]:
            print(f"\n❌ Test failed at {tokens} tokens, stopping.")
            break
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print("=" * 60)
    for result in results:
        if result["success"]:
            print(f"✅ {result['target_tokens']} tokens:")
            print(f"   Sequence: 1 to {result['highest_number_in_sequence']}")
            print(f"   Model response numbers: {result['numbers_in_response']}")
        else:
            print(f"❌ {result['target_tokens']} tokens: FAILED")
    
    return results


if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    results = run_debug_tests()
