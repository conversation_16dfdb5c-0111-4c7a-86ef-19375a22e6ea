#!/usr/bin/env python3
"""
Context Test with Cache Clearing

This test runs individual context tests with cache clearing between each test
by stopping and starting main.py to ensure fresh LLM responses.
"""

import json
import os
import signal
import subprocess
import sys
import time
import uuid

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llamacpp_provider import LlamaCppProvider
from logger.get_logger import get_logger
import config


class CacheClearingTester:
    def __init__(self):
        self.logger = get_logger()
        self.main_process = None
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def start_main_py(self):
        """Start main.py in the background."""
        self.logger.info("Starting main.py...")

        # Change to project directory and activate venv, then start main.py
        cmd = f"cd {self.project_root} && source .venv-dabot-agent/bin/activate && python main.py"

        self.main_process = subprocess.Popen(
                cmd,
                shell = True,
                stdout = subprocess.PIPE,
                stderr = subprocess.PIPE,
                preexec_fn = os.setsid  # Create new process group
        )

        # Give it time to start
        time.sleep(3)

        # Check if it's running
        if self.main_process.poll() is None:
            self.logger.info("main.py started successfully")
            return True
        else:
            stdout, stderr = self.main_process.communicate()
            self.logger.error(f"Failed to start main.py: {stderr.decode()}")
            return False

    def stop_main_py(self):
        """Stop main.py and clear cache."""
        if self.main_process and self.main_process.poll() is None:
            self.logger.info("Stopping main.py...")

            # Kill the entire process group
            try:
                os.killpg(os.getpgid(self.main_process.pid), signal.SIGTERM)

                # Wait for it to stop
                try:
                    self.main_process.wait(timeout = 5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't stop
                    os.killpg(os.getpgid(self.main_process.pid), signal.SIGKILL)
                    self.main_process.wait()

                self.logger.info("main.py stopped successfully")

            except Exception as e:
                self.logger.error(f"Error stopping main.py: {e}")

            self.main_process = None

        # Additional cache clearing - wait a moment
        time.sleep(2)

    def test_single_token_count(self, target_tokens: int, test_id: str):
        """Test a single token count with fresh cache."""
        self.logger.info(f"Testing {target_tokens} tokens (Test {test_id})")

        # Stop main.py to clear cache
        self.stop_main_py()

        # Wait for cache to clear
        time.sleep(2)

        # Start main.py fresh
        if not self.start_main_py():
            return {
                    "test_id"      : test_id,
                    "target_tokens": target_tokens,
                    "success"      : False,
                    "error"        : "Failed to start main.py"
            }

        # Wait for startup - longer wait to ensure server is fully ready
        time.sleep(5)

        try:
            # Create fresh LLM instance
            provider = LlamaCppProvider(config = config.get_model_config())
            llm = provider.get_llm()

            # Verify we have a valid context length
            if llm.prompt_manager.max_context_tokens is None:
                return {
                        "test_id"      : test_id,
                        "target_tokens": target_tokens,
                        "success"      : False,
                        "error"        : "Failed to get context length from server",
                        "cache_cleared": True
                }

            # Generate unique prompt
            unique_id = str(uuid.uuid4())[:8]
            base_prompt = f"Test {test_id} (ID: {unique_id}): What is the highest number you can see?\n\nNumbers: "

            # Build number sequence to reach target tokens
            numbers = []
            number = 1
            while True:
                test_numbers = numbers + [str(number)]
                test_prompt = base_prompt + " ".join(test_numbers) + "\n\nAnswer:"
                token_count = llm.prompt_manager.count_tokens(test_prompt)

                if token_count >= target_tokens:
                    break

                numbers.append(str(number))
                number += 1

            final_prompt = base_prompt + " ".join(numbers) + "\n\nAnswer:"
            final_token_count = llm.prompt_manager.count_tokens(final_prompt)
            highest_in_sequence = number - 1

            print(f"🧪 Test {test_id}: {target_tokens} tokens")
            print(f"   Unique ID: {unique_id}")
            print(f"   Actual tokens: {final_token_count}")
            print(f"   Sequence: 1 to {highest_in_sequence}")
            print(f"   Cache cleared: ✅")

            # Record start time
            start_time = time.time()

            # Send to LLM
            from langchain.schema.messages import HumanMessage
            messages = [HumanMessage(content = final_prompt)]

            print(f"   🚀 Sending request at {time.strftime('%H:%M:%S')}...")
            result = llm._generate(messages)

            # Record end time
            end_time = time.time()
            response_time = end_time - start_time

            response = result.generations[0].message.content.strip()

            print(f"   ⏱️  Response time: {response_time:.2f} seconds")
            print(f"   📤 LLM Response: '{response}'")

            # Extract numbers from response
            import re
            numbers_in_response = re.findall(r'\b\d+\b', response)

            if numbers_in_response:
                highest_response_number = max(int(n) for n in numbers_in_response)
                visibility_percentage = (highest_response_number / highest_in_sequence) * 100
            else:
                highest_response_number = None
                visibility_percentage = 0

            print(f"   📊 Highest number seen: {highest_response_number}")
            print(f"   📈 Visibility: {visibility_percentage:.1f}%")

            # Check if response time is suspiciously fast
            if response_time < 5.0:
                print(f"   ⚠️  WARNING: Response time {response_time:.2f}s might indicate caching!")
            else:
                print(f"   ✅ Response time {response_time:.2f}s looks realistic")

            return {
                    "test_id"                : test_id,
                    "unique_id"              : unique_id,
                    "target_tokens"          : target_tokens,
                    "actual_tokens"          : final_token_count,
                    "highest_in_sequence"    : highest_in_sequence,
                    "model_response"         : response,
                    "highest_response_number": highest_response_number,
                    "visibility_percentage"  : round(visibility_percentage, 1),
                    "response_time_seconds"  : round(response_time, 2),
                    "success"                : True,
                    "cache_cleared"          : True
            }

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {
                    "test_id"      : test_id,
                    "target_tokens": target_tokens,
                    "success"      : False,
                    "error"        : str(e),
                    "cache_cleared": True
            }

    def run_cache_clearing_tests(self):
        """Run the complete test suite with cache clearing."""

        print("🔬 CONTEXT TEST WITH CACHE CLEARING")
        print("=" * 80)
        print("Each test will stop/start main.py to ensure fresh cache...")
        print()

        # Test different token counts - extended range to test full context limits
        test_cases = [
                (400, "T01"),
                (500, "T02"),
                (600, "T03"),
                (700, "T04"),
                (800, "T05"),
                (900, "T06"),
                (1000, "T07"),
                (1200, "T08"),
                (1300, "T09"),
                (1400, "T10"),
                (1500, "T11"),
                (1600, "T12"),
                (1700, "T13"),
                (1800, "T14"),
                (1900, "T15"),
                (2000, "T16"),
                (2100, "T17"),
                (2200, "T18")
        ]

        results = []

        for target_tokens, test_id in test_cases:
            print(f"\n{'=' * 60}")
            result = self.test_single_token_count(target_tokens, test_id)
            results.append(result)

            if not result["success"]:
                print(f"❌ Test {test_id} failed, stopping tests.")
                break

            print(f"✅ Test {test_id} completed")
            print()

        # Stop main.py after all tests
        self.stop_main_py()

        # Analysis
        print(f"\n📊 CACHE-CLEARED TEST ANALYSIS:")
        print("=" * 60)

        successful_results = [r for r in results if r["success"]]

        if successful_results:
            # Response time analysis
            response_times = [r["response_time_seconds"] for r in successful_results]
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)

            print(f"⏱️  Response Time Analysis:")
            print(f"   Average: {avg_time:.2f}s")
            print(f"   Range: {min_time:.2f}s - {max_time:.2f}s")

            # Check for suspiciously fast responses
            fast_responses = [r for r in successful_results if r["response_time_seconds"] < 5.0]
            if fast_responses:
                print(f"   ⚠️  {len(fast_responses)} tests had suspiciously fast responses (<5s)")
            else:
                print(f"   ✅ All responses had realistic times (>5s)")

            # Visibility analysis
            print(f"\n📈 Visibility Analysis:")
            for result in successful_results:
                status_icon = "✅" if result["visibility_percentage"] >= 95 else "🔴"
                print(f"   {status_icon} Test {result['test_id']} ({result['target_tokens']} tokens): "
                      f"{result['visibility_percentage']}% visibility, "
                      f"{result['response_time_seconds']}s")

            # Find practical limit
            good_results = [r for r in successful_results if r["visibility_percentage"] >= 95]
            if good_results:
                max_good_tokens = max(r["target_tokens"] for r in good_results)
                print(f"\n🎯 PRACTICAL LIMIT: {max_good_tokens} tokens (with >95% visibility)")

            # Compare with previous results
            print(f"\n🔍 CACHE IMPACT ASSESSMENT:")
            print(f"   All tests used fresh cache: ✅")
            print(f"   Response times are realistic: {'✅' if min_time >= 5.0 else '⚠️'}")
            print(f"   No caching artifacts detected: {'✅' if not fast_responses else '⚠️'}")

        # Save results
        results_file = os.path.join(os.path.dirname(__file__), "cache_cleared_results.json")
        with open(results_file, 'w') as f:
            json.dump({
                    "timestamp"            : str(os.popen('date').read().strip()),
                    "test_description"     : "Context test with cache clearing between each test",
                    "cache_clearing_method": "Stop/start main.py between tests",
                    "test_results"         : results
            }, f, indent = 2)

        print(f"\n📄 Detailed results saved to: {results_file}")

        return results


def main():
    """Main function to run cache clearing tests."""
    import logging
    logging.basicConfig(level = logging.INFO, format = '%(asctime)s - %(levelname)s - %(message)s')

    tester = CacheClearingTester()

    try:
        results = tester.run_cache_clearing_tests()

        # Quick summary
        successful = [r for r in results if r["success"]]
        if successful:
            print(f"\n🎯 QUICK SUMMARY:")
            print(f"   Tests completed: {len(successful)}")

            # Show practical limit
            good_visibility = [r for r in successful if r["visibility_percentage"] >= 95]
            if good_visibility:
                max_tokens = max(r["target_tokens"] for r in good_visibility)
                print(f"   Safe token limit: {max_tokens} tokens")

            # Show response time assessment
            response_times = [r["response_time_seconds"] for r in successful]
            avg_time = sum(response_times) / len(response_times)
            print(f"   Average response time: {avg_time:.1f}s")
            print(f"   Cache clearing: ✅ Effective")

    except Exception as e:
        print(f"❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Ensure main.py is stopped
        tester.stop_main_py()


if __name__ == "__main__":
    main()
