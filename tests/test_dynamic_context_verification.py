#!/usr/bin/env python3
"""
Dynamic Context Verification Test

Tests the improved dynamic context size fetching and verification system.
This test verifies that the system correctly:
1. Fetches context size from server dynamically
2. Uses the correct context limits
3. <PERSON><PERSON> prompts appropriately within the dynamic limits
"""

import sys
import os
import json
import time
import uuid

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llamacpp_provider import LlamaCppProvider
from models.prompt_manager import PromptManager
from utils.model_info import get_server_info, get_context_length_from_server
from logger.get_logger import get_logger
import config


def test_dynamic_context_setup():
    """Test that dynamic context fetching is working correctly."""
    logger = get_logger()
    
    print("🔧 TESTING DYNAMIC CONTEXT SETUP")
    print("=" * 60)
    
    # Test server info fetching
    print("1. Fetching server information...")
    server_info = get_server_info()
    
    print(f"   Server available: {server_info['server_available']}")
    if server_info['server_available']:
        print(f"   Model name: {server_info['model_name']}")
        print(f"   Context length: {server_info['context_length']}")
    else:
        print("   ❌ Server not available!")
        return None
    
    # Test dynamic context length fetching
    print("\n2. Testing dynamic context length fetching...")
    dynamic_context = get_context_length_from_server()
    print(f"   Dynamic context length: {dynamic_context}")
    
    # Test LLM creation with dynamic context
    print("\n3. Testing LLM creation with dynamic context...")
    provider = LlamaCppProvider(config=config.get_model_config())
    llm = provider.get_llm()
    
    print(f"   LLM prompt_manager max_context_tokens: {llm.prompt_manager.max_context_tokens}")
    print(f"   LLM prompt_manager reserved_tokens: {llm.prompt_manager.reserved_tokens}")
    
    return {
        "server_info": server_info,
        "dynamic_context": dynamic_context,
        "llm_context": llm.prompt_manager.max_context_tokens,
        "llm": llm
    }


def test_numbers_sequence_with_dynamic_context(setup_info):
    """Test using the numbers.txt file with dynamic context."""
    logger = get_logger()
    
    print(f"\n🔢 TESTING NUMBERS SEQUENCE WITH DYNAMIC CONTEXT")
    print("=" * 60)
    
    if not setup_info:
        print("❌ Cannot run test - setup failed")
        return None
    
    llm = setup_info["llm"]
    dynamic_context = setup_info["dynamic_context"]
    
    # Read numbers from file
    numbers_file = os.path.join(os.path.dirname(__file__), "numbers.txt")
    if not os.path.exists(numbers_file):
        print(f"❌ Numbers file not found: {numbers_file}")
        return None
    
    with open(numbers_file, 'r') as f:
        numbers_content = f.read().strip()
    
    print(f"Numbers file content length: {len(numbers_content)} characters")
    
    # Create test prompt
    unique_id = str(uuid.uuid4())[:8]
    test_prompt = f"Test ID {unique_id}: What is the highest number you can see in this list?\n\nNumbers: {numbers_content}\n\nAnswer:"
    
    # Count tokens
    token_count = llm.prompt_manager.count_tokens(test_prompt)
    print(f"Test prompt token count: {token_count}")
    print(f"Dynamic context limit: {dynamic_context}")
    print(f"Available tokens for response: {dynamic_context - token_count - llm.prompt_manager.reserved_tokens}")
    
    # Check if prompt fits in context
    if token_count > (dynamic_context - llm.prompt_manager.reserved_tokens):
        print(f"⚠️  Prompt ({token_count} tokens) exceeds context limit ({dynamic_context - llm.prompt_manager.reserved_tokens} available)")
        return None
    
    # Send to LLM
    print(f"\n🚀 Sending prompt to LLM...")
    start_time = time.time()
    
    try:
        from langchain.schema.messages import HumanMessage
        messages = [HumanMessage(content=test_prompt)]
        
        result = llm._generate(messages)
        end_time = time.time()
        response_time = end_time - start_time
        
        response = result.generations[0].message.content.strip()
        
        print(f"⏱️  Response time: {response_time:.2f} seconds")
        print(f"📤 LLM Response: '{response}'")
        
        # Extract highest number from response
        import re
        numbers_in_response = re.findall(r'\b\d+\b', response)
        if numbers_in_response:
            highest_response = max(int(n) for n in numbers_in_response)
            print(f"📊 Highest number in response: {highest_response}")
        else:
            highest_response = None
            print(f"📊 No numbers found in response")
        
        # Extract highest number from original sequence
        original_numbers = re.findall(r'\b\d+\b', numbers_content)
        if original_numbers:
            highest_original = max(int(n) for n in original_numbers)
            print(f"📈 Highest number in original sequence: {highest_original}")
            
            if highest_response:
                visibility_percentage = (highest_response / highest_original) * 100
                print(f"📈 Visibility percentage: {visibility_percentage:.1f}%")
            else:
                visibility_percentage = 0
        else:
            highest_original = None
            visibility_percentage = 0
        
        return {
            "success": True,
            "token_count": token_count,
            "dynamic_context": dynamic_context,
            "response_time": response_time,
            "response": response,
            "highest_response": highest_response,
            "highest_original": highest_original,
            "visibility_percentage": visibility_percentage
        }
        
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"❌ Error after {response_time:.2f}s: {str(e)}")
        return {
            "success": False,
            "token_count": token_count,
            "dynamic_context": dynamic_context,
            "response_time": response_time,
            "error": str(e)
        }


def test_incremental_token_limits(setup_info):
    """Test different token limits to find the practical boundary."""
    logger = get_logger()
    
    print(f"\n📏 TESTING INCREMENTAL TOKEN LIMITS")
    print("=" * 60)
    
    if not setup_info:
        print("❌ Cannot run test - setup failed")
        return []
    
    llm = setup_info["llm"]
    dynamic_context = setup_info["dynamic_context"]
    
    # Test different token counts
    test_token_counts = [400, 500, 600, 700, 800, 900, 1000]
    
    # Filter test counts based on dynamic context
    max_safe_tokens = dynamic_context - llm.prompt_manager.reserved_tokens - 100  # Safety margin
    test_token_counts = [t for t in test_token_counts if t <= max_safe_tokens]
    
    print(f"Testing token counts: {test_token_counts}")
    print(f"Dynamic context limit: {dynamic_context}")
    print(f"Max safe tokens: {max_safe_tokens}")
    
    results = []
    
    for target_tokens in test_token_counts:
        print(f"\n🧪 Testing {target_tokens} tokens...")
        
        # Generate prompt with target token count
        unique_id = str(uuid.uuid4())[:8]
        base_prompt = f"Test {target_tokens}T (ID: {unique_id}): What is the highest number you can see?\n\nNumbers: "
        
        # Build number sequence to reach target tokens
        numbers = []
        number = 1
        while True:
            test_numbers = numbers + [str(number)]
            test_prompt = base_prompt + " ".join(test_numbers) + "\n\nAnswer:"
            token_count = llm.prompt_manager.count_tokens(test_prompt)
            
            if token_count >= target_tokens:
                break
            
            numbers.append(str(number))
            number += 1
        
        final_prompt = base_prompt + " ".join(numbers) + "\n\nAnswer:"
        final_token_count = llm.prompt_manager.count_tokens(final_prompt)
        highest_in_sequence = number - 1
        
        print(f"   Generated sequence: 1 to {highest_in_sequence} ({final_token_count} tokens)")
        
        # Test with LLM
        start_time = time.time()
        try:
            from langchain.schema.messages import HumanMessage
            messages = [HumanMessage(content=final_prompt)]
            
            result = llm._generate(messages)
            end_time = time.time()
            response_time = end_time - start_time
            
            response = result.generations[0].message.content.strip()
            
            # Extract highest number from response
            import re
            numbers_in_response = re.findall(r'\b\d+\b', response)
            if numbers_in_response:
                highest_response = max(int(n) for n in numbers_in_response)
                visibility = (highest_response / highest_in_sequence) * 100
            else:
                highest_response = None
                visibility = 0
            
            status = "✅" if visibility >= 95 else "🔴"
            print(f"   {status} Response: {highest_response}, Visibility: {visibility:.1f}%, Time: {response_time:.1f}s")
            
            results.append({
                "target_tokens": target_tokens,
                "actual_tokens": final_token_count,
                "highest_in_sequence": highest_in_sequence,
                "highest_response": highest_response,
                "visibility_percentage": visibility,
                "response_time": response_time,
                "success": True
            })
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            print(f"   ❌ Failed: {str(e)}")
            
            results.append({
                "target_tokens": target_tokens,
                "actual_tokens": final_token_count,
                "highest_in_sequence": highest_in_sequence,
                "highest_response": None,
                "visibility_percentage": 0,
                "response_time": response_time,
                "success": False,
                "error": str(e)
            })
        
        # Small delay between tests
        time.sleep(1)
    
    return results


def run_dynamic_context_verification():
    """Run the complete dynamic context verification test suite."""
    
    print("🔬 DYNAMIC CONTEXT VERIFICATION TEST SUITE")
    print("=" * 80)
    print("Testing improved dynamic context size fetching and handling...")
    print()
    
    # Test 1: Dynamic context setup
    setup_info = test_dynamic_context_setup()
    
    # Test 2: Numbers sequence test
    numbers_result = test_numbers_sequence_with_dynamic_context(setup_info)
    
    # Test 3: Incremental token limits
    incremental_results = test_incremental_token_limits(setup_info)
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 60)
    
    if setup_info:
        print(f"✅ Dynamic context setup: SUCCESS")
        print(f"   Server context: {setup_info['dynamic_context']} tokens")
        print(f"   LLM context: {setup_info['llm_context']} tokens")
    else:
        print(f"❌ Dynamic context setup: FAILED")
    
    if numbers_result:
        if numbers_result["success"]:
            print(f"✅ Numbers sequence test: SUCCESS")
            print(f"   Visibility: {numbers_result['visibility_percentage']:.1f}%")
            print(f"   Response time: {numbers_result['response_time']:.1f}s")
        else:
            print(f"❌ Numbers sequence test: FAILED")
            print(f"   Error: {numbers_result.get('error', 'Unknown')}")
    
    if incremental_results:
        successful_tests = [r for r in incremental_results if r["success"]]
        good_visibility = [r for r in successful_tests if r["visibility_percentage"] >= 95]
        
        print(f"✅ Incremental tests: {len(successful_tests)}/{len(incremental_results)} successful")
        if good_visibility:
            max_good_tokens = max(r["target_tokens"] for r in good_visibility)
            print(f"   Practical limit: {max_good_tokens} tokens (>95% visibility)")
        
        # Show trend
        print(f"   Visibility trend:")
        for result in incremental_results:
            if result["success"]:
                status = "✅" if result["visibility_percentage"] >= 95 else "🔴"
                print(f"     {status} {result['target_tokens']} tokens → {result['visibility_percentage']:.1f}%")
    
    # Save results
    results_file = os.path.join(os.path.dirname(__file__), "dynamic_context_results.json")
    with open(results_file, 'w') as f:
        json.dump({
            "timestamp": str(os.popen('date').read().strip()),
            "test_description": "Dynamic context verification with improved system",
            "setup_info": setup_info,
            "numbers_result": numbers_result,
            "incremental_results": incremental_results
        }, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return {
        "setup_info": setup_info,
        "numbers_result": numbers_result,
        "incremental_results": incremental_results
    }


if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        results = run_dynamic_context_verification()
        
        # Quick assessment
        if results["setup_info"] and results["setup_info"]["dynamic_context"]:
            print(f"\n🎯 QUICK ASSESSMENT:")
            print(f"   Dynamic context fetching: ✅ WORKING")
            print(f"   Server context: {results['setup_info']['dynamic_context']} tokens")
            
            if results["incremental_results"]:
                good_results = [r for r in results["incremental_results"] if r["success"] and r["visibility_percentage"] >= 95]
                if good_results:
                    max_safe = max(r["target_tokens"] for r in good_results)
                    print(f"   Practical safe limit: {max_safe} tokens")
                else:
                    print(f"   ⚠️  No tests achieved >95% visibility")
        else:
            print(f"\n❌ ASSESSMENT: Dynamic context fetching failed")
        
    except Exception as e:
        print(f"❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
