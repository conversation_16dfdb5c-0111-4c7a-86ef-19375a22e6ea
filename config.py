"""
Configuration constants for the DaBot Agent.

This module contains all configuration constants and settings for the application.
"""

from typing import Dict, Any

APP_NAME = "DaBot Agent"
LLAMACPP_API_URL = "http://172.16.0.111:11111/completion"
LLAMACPP_MAX_TOKENS = 250  # n_predict
MODEL_PROVIDER = "llamacpp"
MODEL_NAME = "ollama7b-finance"  # Can be fetched from remote server via GET /props endpoint (model_path field)
MAX_ITERATIONS = "1"
VERBOSE = "true"
LOG_LEVEL = "DEBUG"
LOG_FILE = "dabot-agent.log"
# MAX_CONTEXT_TOKENS can be fetched from remote server via GET /props endpoint (n_ctx field)
# The server's -c parameter sets this value, accessible at runtime via API
# Example: GET http://172.16.0.111:11111/props returns {"default_generation_settings":{"n_ctx":1024,...}}
MAX_CONTEXT_TOKENS = 2048
RESERVED_TOKENS = 50
APP_VERSION = "0.1"


# =============================================================================
# Helper Functions
# =============================================================================
def get_model_config() -> Dict[str, Any]:
    """Get model configuration dictionary."""
    return {
            "provider"  : MODEL_PROVIDER,
            "name"      : MODEL_NAME,
            "max_tokens": LLAMACPP_MAX_TOKENS,
            "api_url"   : LLAMACPP_API_URL
    }


def get_agent_config() -> Dict[str, Any]:
    """Get agent configuration dictionary."""
    return {
            "max_iterations": MAX_ITERATIONS,
            "verbose"       : VERBOSE
    }


def get_logging_config() -> Dict[str, Any]:
    """Get logging configuration dictionary."""
    return {
            "level": LOG_LEVEL,
            "file" : LOG_FILE
    }
