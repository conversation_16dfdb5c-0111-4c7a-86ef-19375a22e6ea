"""
LLaMA.cpp model provider implementation with dynamic prompt length tracking.
"""
import json
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator

import requests
from langchain.chat_models.base import BaseChatModel
from langchain.schema.messages import (
    BaseMessage, AIMessage, AIMessageChunk,
    SystemMessage, HumanMessage, ToolMessage
)
from langchain.schema.output import Chat<PERSON>eneration, ChatGenerationChunk, ChatResult
from langchain.schema.runnable import RunnableConfig
from langchain.tools import BaseTool

from config import LLAMACPP_MAX_TOKENS, MAX_CONTEXT_TOKENS, RESERVED_TOKENS
from logger.get_logger import log
from .prompt_manager import PromptManager


class LlamaCppChatModel(BaseChatModel):
    """LLaMA.cpp Chat Model implementation."""

    api_url: str
    max_tokens: int = LLAMACPP_MAX_TOKENS
    tools: List[BaseTool] = []
    tool_choice: Optional[Union[str, Dict]] = None

    # Initialize PromptManager with max context tokens and reserved tokens
    prompt_manager: PromptManager = PromptManager(max_context_tokens = MAX_CONTEXT_TOKENS,
                                                  reserved_tokens = RESERVED_TOKENS)

    @property
    def _llm_type(self) -> str:
        return "llama.cpp.chat"

    @log
    def bind_tools(self, tools: List[BaseTool], tool_choice: Optional[Union[str, Dict]] = None,
                   **kwargs: Any) -> BaseChatModel:
        """Bind tools to the chat model.

        Args:
            tools: The tools to bind to the chat model
            tool_choice: The tool choice to use ("auto", "any", or specific tool name)

        Returns:
            The chat model with tools bound
        """
        new_instance = self.copy()
        new_instance.tools = tools
        new_instance.tool_choice = tool_choice
        return new_instance

    def with_config(self, config: RunnableConfig) -> BaseChatModel:
        """Return a new chat model with the specified config."""
        new_instance = self.copy()
        if "tools" in config:
            new_instance.tools = config["tools"]
        return new_instance

    def copy(self) -> BaseChatModel:
        """Create a copy of the chat model."""
        return LlamaCppChatModel(
                api_url = self.api_url,
                max_tokens = self.max_tokens,
                tools = self.tools.copy() if self.tools else [],
                tool_choice = self.tool_choice,
        )

    def _generate(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager = None,
            **kwargs: Any,
    ) -> ChatResult:
        """Call the LLaMA.cpp API and generate a chat result."""

        headers = {"Content-Type": "application/json"}

        # Convert messages to a prompt format that llama.cpp can understand
        prompt = self._convert_messages_to_prompt(messages)

        # Requested max tokens from kwargs or default
        requested_max_tokens = kwargs.get("max_tokens", self.max_tokens)

        # Dynamically adjust max_tokens based on prompt length
        adjusted_max_tokens = self.prompt_manager.adjust_max_tokens(prompt, requested_max_tokens)

        # Raise error if prompt too long to generate any tokens
        if adjusted_max_tokens <= 0:
            raise ValueError(
                    "Prompt is too long for the context window. "
                    "Please shorten the conversation history or reduce max_tokens."
            )

        # Only send minimal parameters - let remote model use its defaults
        data = {
                "prompt"   : prompt,
                "n_predict": adjusted_max_tokens,  # Only parameter we control
                "stream"   : False,  # Keep this for simplicity
        }

        # Add stop sequences if provided (optional)
        if stop:
            data["stop"] = stop

        try:
            response = requests.post(self.api_url, headers = headers, data = json.dumps(data))

            # Log full raw response for debugging
            from logger.get_logger import get_logger
            logger = get_logger()
            logger.debug(f"LLM raw response status: {response.status_code}")
            logger.debug(f"LLM raw response headers: {dict(response.headers)}")
            logger.debug(f"LLM raw response body: {response.text}")

            response.raise_for_status()
            response_json = response.json()

            content = ""
            if "content" in response_json:
                content = response_json["content"]
            elif "response" in response_json:
                content = response_json["response"]
            elif "text" in response_json:
                content = response_json["text"]
            elif "choices" in response_json and len(response_json["choices"]) > 0:
                content = response_json["choices"][0]["text"]
            else:
                content = str(response_json)

            content = self._clean_response_content(content)

            generation = ChatGeneration(
                    message = AIMessage(content = content),
            )

            return ChatResult(generations = [generation])
        except Exception as e:
            raise ValueError(f"Error calling LLaMA.cpp API: {str(e)}")

    def _clean_response_content(self, content: str) -> str:
        """Clean up the response content to remove tool descriptions and repeated prompts."""
        if "Available Tools:" in content:
            content = content.split("Available Tools:")[0].strip()

        if "User:" in content:
            content = content.split("User:")[0].strip()

        if content.startswith("Assistant:"):
            content = content[len("Assistant:"):].strip()

        return content

    @log
    def _convert_messages_to_prompt(self, messages: List[BaseMessage]) -> str:
        """Convert a list of messages to a prompt format that llama.cpp can understand."""
        # No hardcoded system prompt - let the node handle system messages
        prompt = ""

        if hasattr(self, "tools") and self.tools:
            prompt += "You have access to the following tools:\n"
            for tool in self.tools:
                prompt += f"- {tool.name}: {tool.description}\n"
            prompt += "\nOnly use these tools when necessary. Respond directly when you know the answer.\n\n"

        for message in messages:
            if isinstance(message, SystemMessage):
                prompt += f"System: {message.content}\n\n"
            elif isinstance(message, HumanMessage):
                prompt += f"User: {message.content}\n\n"
            elif isinstance(message, AIMessage):
                prompt += f"Assistant: {message.content}\n\n"
            elif isinstance(message, ToolMessage):
                prompt += f"Tool ({message.tool_call_id}): {message.content}\n\n"

        prompt += "Assistant: "
        return prompt

    def _stream(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager = None,
            **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        """Stream the response from the LLaMA.cpp API."""
        result = self._generate(messages, stop, run_manager, **kwargs)
        if result.generations:
            yield ChatGenerationChunk(message = AIMessageChunk(content = result.generations[0].message.content))

    async def _astream(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager = None,
            **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        """Async stream the response from the LLaMA.cpp API."""
        result = self._generate(messages, stop, run_manager, **kwargs)
        if result.generations:
            yield ChatGenerationChunk(message = AIMessageChunk(content = result.generations[0].message.content))


class LlamaCppProvider:
    """LLaMA.cpp model provider implementation."""

    def __init__(self, config: Dict[str, Any], api_url: Optional[str] = None):
        """Initialize the LLaMA.cpp model provider."""
        self.config = config
        self.api_url = api_url or "http://172.16.0.111:11111/completion"

    @log
    def get_llm(self) -> LlamaCppChatModel:
        """Get the LLaMA.cpp language model instance."""
        return LlamaCppChatModel(
                api_url = self.api_url,
                # temperature = let remote model use its defaults
                # TODO: learn how to implement a stop sequence (stop token) like <|end|> and implement it
                max_tokens = LLAMACPP_MAX_TOKENS
        )
