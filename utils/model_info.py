"""
Utility functions for fetching model information from remote llama.cpp server.

This module provides functions to dynamically fetch model parameters like
model name and context length from the remote server
"""

import json
import os
import sys
from typing import Dict, Any, Optional

import requests

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import LLAMACPP_API_URL
from logger.get_logger import log, get_logger


@log
def fetch_model_props(api_url: str = None) -> Optional[Dict[str, Any]]:
    """
    Fetch model properties from llama.cpp server /props endpoint.

    Args:
        api_url: API URL base (defaults to LLAMACPP_API_URL)

    Returns:
        Dictionary containing model properties or None if failed
    """
    logger = get_logger()

    if api_url is None:
        api_url = LLAMACPP_API_URL

    # Convert completion endpoint to props endpoint
    props_url = api_url.replace('/completion', '/props')

    try:
        logger.debug(f"Fetching model properties from: {props_url}")
        response = requests.get(props_url, timeout = 5)
        response.raise_for_status()

        props = response.json()
        logger.debug(f"Successfully fetched model properties: {json.dumps(props, indent = 2)}")
        return props

    except requests.exceptions.RequestException as e:
        logger.warning(f"Failed to fetch model properties from {props_url}: {e}")
        return None
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse model properties JSON: {e}")
        return None


@log
def get_model_name_from_server(api_url: str = None) -> Optional[str]:
    """
    Get the actual model name from the remote server.
    
    Args:
        api_url: API URL base (defaults to LLAMACPP_API_URL)
        
    Returns:
        Model name extracted from model_path or None if failed
    """
    logger = get_logger()

    props = fetch_model_props(api_url)
    if not props:
        return None

    model_path = props.get('model_path')
    if not model_path:
        logger.warning("No model_path found in server properties")
        return None

    # Extract model name from path
    model_name = os.path.basename(model_path)
    logger.info(f"Extracted model name from server: {model_name}")
    return model_name


@log
def get_context_length_from_server(api_url: str = None) -> Optional[int]:
    """
    Get the actual context length from the remote server.

    Args:
        api_url: API URL base (defaults to LLAMACPP_API_URL)

    Returns:
        Context length (n_ctx) or None if failed
    """
    logger = get_logger()

    props = fetch_model_props(api_url)
    if not props:
        return None

    # Context length is in default_generation_settings.n_ctx
    default_settings = props.get('default_generation_settings', {})
    n_ctx = default_settings.get('n_ctx')

    if n_ctx is None:
        logger.warning("No n_ctx found in server properties")
        return None

    logger.info(f"Extracted context length from server: {n_ctx}")
    return n_ctx


@log
def get_server_info(api_url: str = None) -> Dict[str, Any]:
    """
    Get comprehensive server information including model name and context length.
    
    Args:
        api_url: API URL base (defaults to LLAMACPP_API_URL)
        
    Returns:
        Dictionary with server information
    """
    logger = get_logger()

    info = {
            'model_name'      : None,
            'context_length'  : None,
            'server_available': False,
            'props'           : None
    }

    props = fetch_model_props(api_url)
    if props:
        info['server_available'] = True
        info['props'] = props
        info['model_name'] = get_model_name_from_server(api_url)
        info['context_length'] = get_context_length_from_server(api_url)

    logger.info(f"Server info summary: {info}")
    return info


# Example usage function for demonstration
@log
def demonstrate_dynamic_config():
    """
    Demonstrate how to use dynamic model configuration.
    This shows how the TODOs in config.py could be implemented.
    """
    logger = get_logger()

    logger.info("=== Demonstrating Dynamic Model Configuration ===")

    # Get server info
    server_info = get_server_info()

    if server_info['server_available']:
        logger.info("✅ Server is available - can use dynamic configuration")
        logger.info(f"   Model name: {server_info['model_name']}")
        logger.info(f"   Context length: {server_info['context_length']}")

        # Show how this could replace static config
        logger.info("💡 This could replace static config values:")
        logger.info(f"   MODEL_NAME = '{server_info['model_name']}'")
        logger.info(f"   MAX_CONTEXT_TOKENS = {server_info['context_length']}")

    else:
        logger.warning("❌ Server not available - falling back to static configuration")
        from config import MODEL_NAME, MAX_CONTEXT_TOKENS
        logger.info(f"   Using static MODEL_NAME: {MODEL_NAME}")
        logger.info(f"   Using static MAX_CONTEXT_TOKENS: {MAX_CONTEXT_TOKENS}")


if __name__ == "__main__":
    # Run demonstration when script is executed directly
    demonstrate_dynamic_config()
